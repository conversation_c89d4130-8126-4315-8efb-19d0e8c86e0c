<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.socialplay.gpark">


    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission
        android:name="android.permission.BODY_SENSORS"
        tools:node="remove" />
    <uses-permission
        android:name="com.android.voicemail.permission.ADD_VOICEMAIL"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.USE_SIP"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_CONTACTS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_CELL_BROADCASTS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.REQUEST_INSTALL_PACKAGES"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.INSTALL_PACKAGES"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.DELETE_PACKAGES"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
    <uses-permission
        android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" />

    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:node="remove" />
    <uses-permission
        android:name="com.android.vending.BILLING"
        tools:node="remove" />
    <uses-permission
        android:name="com.android.vending.CHECK_LICENSE"
        tools:node="remove" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <!--如果设置了target >= 28 如果需要启动后台定位则必须声明这个权限-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE"/>

    <queries>
        <package android:name="com.snapchat.android" />
        <package android:name="com.tencent.mm" />
        <package android:name="com.tencent.mobileqq" />
        <package android:name="com.discord" />
        <package android:name="com.zhiliaoapp.musically" />
        <package android:name="com.ss.android.ugc.trill" />
        <package android:name="com.twitter.android" />
        <package android:name="com.facebook.katana" />
        <package android:name="com.google.android.youtube" />
        <package android:name="com.zhiliaoapp.musically" />
        <package android:name="com.ss.android.ugc.trill" />
    </queries>

    <queries>
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
    </queries>

    <queries>
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE"/>
        </intent>
        <intent>
            <action android:name="android.media.action.VIDEO_CAPTURE"/>
        </intent>
    </queries>

    <meta-data
        android:name="asset_statements"
        android:resource="@string/asset_statements" />
    <application
        android:name="com.socialplay.gpark.MetaApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.MetaApp"
        android:usesCleartextTraffic="true"
        tools:replace="android:theme,android:allowBackup">

        <activity
            android:name=".function.auth.oauth.platform.GoogleCallbackActivity"
            android:theme="@style/OAuthCallbackTheme" />

<!--        <activity-->
<!--            android:name=".ui.main.StyleTestActivity"-->
<!--            android:theme="@style/Theme.MainActivity" />-->

        <activity
            android:name=".function.auth.oauth.platform.FacebookCallbackActivity"
            android:theme="@style/OAuthCallbackTheme" />

        <activity
            android:name=".ui.ad.AdActivity"
            android:configChanges="keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:excludeFromRecents="true"
            android:launchMode="singleTop"
            android:allowTaskReparenting="true"
            android:screenOrientation="behind"
            android:theme="@style/AdFullScreenTheme" />

        <activity android:name=".ui.ad.TestRewardedAdActivity"
            android:configChanges="keyboard|keyboardHidden|locale|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AdFullScreenTheme" />

        <provider
            android:name=".function.analytics.AnalyticsContentProvider"
            android:authorities="${applicationId}.analytics"
            android:exported="false"
            android:multiprocess="false" />
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths"
                tools:replace="android:resource" />
        </provider>

        <activity
            android:name=".ui.editor.tab.FullScreenEditorActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.FullScreen" />

        <activity
            android:name=".ui.entry.GparkEntryActivity"
            android:configChanges="keyboardHidden|keyboard|orientation|screenSize"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/OAuthCallbackTheme" />

        <activity
            android:name=".ui.permission.GamePermissionActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="orientation|keyboardHidden"
            android:taskAffinity="com.meta.card"
            android:theme="@style/wrapperFullScreen" />

        <service
            android:name=".function.record.ScreenRecorderService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

        <service
            android:name="com.meta.verse.bridge.isolate.server.UEService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" />

        <activity android:name=".ui.locale.LanguageRecreateActivity"
            android:theme="@style/Theme.LanguageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"/>

        <activity android:name=".ui.login.LoginActivity"
            android:launchMode="singleTask"
            android:theme="@style/OAuthCallbackTheme"
            android:configChanges="orientation|keyboardHidden|screenSize"/>

        <meta-data
            android:name="com.epicgames.ue4.GameActivity.bUseDisplayCutout"
            android:value="true"
            tools:node="replace" />

        <activity
            android:name="com.epicgames.ue4.GameActivity"
            android:configChanges="mcc|mnc|uiMode|density|screenSize|smallestScreenSize|screenLayout|orientation|keyboardHidden|keyboard"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:maxAspectRatio="2.4"
            android:process=":m"
            android:resizeableActivity="false"
            android:screenOrientation="sensorLandscape"
            android:theme="@style/MWTheme"
            tools:node="replace">
            <meta-data
                android:name="android.app.lib_name"
                android:value="UE4" />
        </activity>

        <activity
            android:name="com.epicgames.ue4.PortraitGameActivity"
            android:configChanges="mcc|mnc|uiMode|density|screenSize|smallestScreenSize|screenLayout|orientation|keyboardHidden|keyboard"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:launchMode="singleTop"
            android:maxAspectRatio="2.4"
            android:process=":m"
            android:resizeableActivity="false"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/MWTheme_Portrait"
            tools:node="replace">
            <meta-data
                android:name="android.app.lib_name"
                android:value="UE4" />
        </activity>
        <activity
            android:name=".ui.plot.chooseimage.PlotChooseImageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:excludeFromRecents="true"
            android:allowTaskReparenting="true"
            android:theme="@style/TransparentTheme" />

        <activity
            android:name=".ui.plot.chooseimage.LocalPictureSelectorActivity"
            android:allowTaskReparenting="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:excludeFromRecents="true"
            android:launchMode="singleTop"
            android:theme="@style/TransparentTheme" />

        <activity
            android:name=".ui.plot.chooseimage.DuplicateImageActivity"
            android:allowTaskReparenting="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:excludeFromRecents="true"
            android:launchMode="singleTop"
            android:theme="@style/TransparentTheme" />

        <service
            android:name=".data.interactor.EditorGameLoadInteractor$StopAvatarService"
            android:exported="false" />

        <receiver
            android:name="com.socialplay.gpark.function.share.platform.SystemShareReceiver"
            android:exported="false" />

        <activity
            android:name=".ui.base.RootNavHostFragmentActivity"
            android:configChanges="keyboardHidden|orientation|screenLayout|screenSize"
            android:launchMode="singleTop"
            android:exported="false"
            android:windowSoftInputMode="adjustResize">
        </activity>

        <activity
            android:name=".ui.base.RootNavHostFragmentHorizontalActivity"
            android:configChanges="keyboardHidden|orientation|screenLayout|screenSize"
            android:launchMode="singleTop"
            android:exported="false"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="adjustResize">
        </activity>

        <activity
            android:name=".ui.web.WebActivity"
            android:allowTaskReparenting="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:launchMode="singleTop"
            >
            <intent-filter>
                <action android:name="${applicationId}.ui.web.jump" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

    </application>

</manifest>