<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.socialplay.gpark.ui.view.MetallicBorderView
        android:id="@+id/metallic_border_view"
        android:layout_width="0dp"
        android:layout_height="26dp"
        android:background="@drawable/bg_like_btn"
        app:blurEnabled="true"
        android:layout_marginHorizontal="-10dp"
        app:blurOverlayColor="@color/black_3"
        app:borderThickness="@dimen/dp_05"
        app:cornerRadius="@dimen/dp_30"
        app:glowColor="@color/black_8"
        app:glowIntensity="0.1"
        app:glowRadius="@dimen/dp_3"
        app:highlightColor="@color/white"
        app:highlightIntensity="1"
        app:highlightWidth="0.8"
        app:layout_constraintBottom_toBottomOf="@id/iv_like"
        app:layout_constraintLeft_toLeftOf="@id/iv_like"
        app:layout_constraintRight_toRightOf="@id/tv_zan"
        app:layout_constraintTop_toTopOf="@id/iv_like"
        app:lightAngle="17"
        app:metallicBaseColor="@color/grey_border_50" />

    <ImageView
        android:id="@+id/iv_like"
        android:layout_width="@dimen/dp_12"
        android:layout_height="@dimen/dp_12"
        android:layout_marginStart="@dimen/dp_10"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_2"
        android:layout_marginBottom="@dimen/dp_6"
        android:src="@drawable/icon_item_like"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.socialplay.gpark.ui.view.MetaTextView
        android:id="@+id/tv_zan"
        style="@style/MetaTextView.S9.PoppinsMedium500"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_14"
        android:layout_marginStart="@dimen/dp_2"
        android:gravity="center_vertical"
        android:shadowColor="@color/black_15"
        android:shadowDx="0"
        android:shadowDy="1"
        android:shadowRadius="1"
        android:textColor="@color/white"
        app:layout_constraintStart_toEndOf="@id/iv_like"
        app:layout_constraintTop_toTopOf="@id/iv_like"
        app:layout_constraintBottom_toBottomOf="@id/iv_like"
        tools:text="a321515135k" />

</merge> 