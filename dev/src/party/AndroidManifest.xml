<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>

    <queries>
        <!-- 微信-qq -->
        <package android:name="com.tencent.mm" />
        <package android:name="com.tencent.mobileqq" />
        <package android:name="com.tencent.tim" />
        <package android:name="com.tencent.minihd.qq" />
        <package android:name="com.tencent.qqlite" />
        <package android:name="com.qzone" />
        <!--抖音-->
        <package android:name="com.ss.android.ugc.aweme" />
        <package android:name="com.ss.android.ugc.aweme.lite" />
        <package android:name="com.ss.android.ugc.aweme.live" />
        <!--快手-->
        <package android:name="com.smile.gifmaker" />
        <package android:name="com.kuaishou.nebula" />
        <!--小红书-->
        <package android:name="com.xingin.xhs" />
    </queries>

    <application>

        <activity
            android:name=".ui.main.MainActivity"
            android:allowNativeHeapPointerTagging="false"
            android:configChanges="keyboardHidden|orientation|screenLayout|screenSize"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="@string/main_activity_screen_orientation"
            android:theme="@style/Theme.MainActivity"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.APP_BROWSER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:host="${SCHEME_HOST}" />
                <data android:scheme="metaapp" />
                <data android:scheme="233party" />
                <data android:scheme="party233" />


                <data android:host="233party" />
                <data android:host="233party.com" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.APP_BROWSER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:host="${SCHEME_HOST}" />
                <data android:scheme="https" />
            </intent-filter>
        </activity>

        <activity
            android:name=".function.auth.oauth.QQCallbackActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:theme="@style/QQCallbackTheme" />

        <activity
            android:name="${applicationId}.wxapi.WXEntryActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:taskAffinity="${applicationId}"
            android:theme="@style/QQCallbackTheme" />

        <activity
            android:name=".ui.compliance.ProtocolWebActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:allowTaskReparenting="true"
            android:launchMode="singleTop"
            android:theme="@style/Theme.FullScreen"
            >
        </activity>

        <activity
            android:name=".function.pay.way.StartAliPayActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:taskAffinity="com.meta.box.pay"
            android:theme="@style/StartPayTheme">
        </activity>

        <activity
            android:name=".function.pay.way.StartWeChatPayActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:taskAffinity="com.meta.box.pay"
            android:theme="@style/StartPayTheme">
        </activity>

        <activity
            android:name="com.meta.box.party.wxapi.WXPayEntryActivity"
            android:configChanges="screenSize|keyboardHidden|orientation"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity="com.meta.box.pay"
            android:theme="@style/QQCallbackTheme" />

        <activity
            android:name=".function.share.callback.WXShareCallbackActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:theme="@style/QQCallbackTheme" />

        <activity
            android:name=".function.share.callback.QQShareCallbackActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:theme="@style/QQCallbackTheme" />

        <activity
            android:name=".function.share.callback.DouYinShareCallbackActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:theme="@style/QQCallbackTheme" />

        <activity
            android:name="${applicationId}.douyinapi.DouYinEntryActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:taskAffinity="${applicationId}" />

        <activity
            android:name=".function.share.callback.KuaishouShareCallbackActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:theme="@style/QQCallbackTheme" />

        <activity
            android:name=".function.share.callback.XhsShareCallbackActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:launchMode="singleTask"
            android:theme="@style/QQCallbackTheme" />

    </application>

</manifest>